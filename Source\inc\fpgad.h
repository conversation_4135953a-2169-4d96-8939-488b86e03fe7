/*!
    \file  main.h
    \brief the header file of main 
*/

/*
    Copyright (C) 2016 GigaDevice

    2016-08-15, V1.0.0, firmware for GD32F4xx
*/

#ifndef __FPGAD_H
#define __FPGAD_H
#include "gd32f4xx_can.h"

#define	c_outputmode_normal		0
#define	c_outputmode_magnet		1
#define	c_outputmode_special	2
#define	mag_calimodel	3

typedef struct _fpgadata {
	unsigned short	DATA_LEN;
	unsigned short	NUM_CLK;
	short	Dtemp_data;
	short	Utemp_data;
	short	accZ_data;
	short	accY_data;
	short	accX_data;
	short	rateZ_data;
	short	rateY_data;
	short	rateX_data;
	unsigned short	hGPSData_gpsweek;
	unsigned short	hGPSData_gpssecond0;
	unsigned short	hGPSData_gpssecond1;
	unsigned short	hGPSData_headingStatus;
	unsigned short	hGPSData_rtkStatus;	
	unsigned short	hGPSData_vn0;
	unsigned short	hGPSData_vn1;
	unsigned short	hGPSData_ve0;
	unsigned short	hGPSData_ve1;
	unsigned short	hGPSData_vu0;
	unsigned short	hGPSData_vu1;
	unsigned short	hGPSData_Heading0;
	unsigned short	hGPSData_Heading1;
	unsigned short	hGPSData_Pitch0;
	unsigned short	hGPSData_Pitch1;
	unsigned short	hGPSData_Roll0;
	unsigned short	hGPSData_Roll1;
	unsigned short	hGPSData_Lat0;
	unsigned short	hGPSData_Lat1;
	unsigned short	hGPSData_Lat2;
	unsigned short	hGPSData_Lat3;
	unsigned short	hGPSData_Lon0;
	unsigned short	hGPSData_Lon1;
	unsigned short	hGPSData_Lon2;
	unsigned short	hGPSData_Lon3;
	unsigned short	hGPSData_Alt0;
	unsigned short	hGPSData_Alt1;
	unsigned short	hGPSData_Alt2;
	unsigned short	hGPSData_Alt3;
	unsigned short	GPGGA_STAR;
	unsigned short	HEADING_BAS0;
	unsigned short	HEADING_BAS1;
	unsigned short	GPRMC_POS;
	unsigned short	GPRMC_LON;
	unsigned short	GPRMC_LAT;
	unsigned short	GPRMC_TRA[3];
//	unsigned short	Heading0;
//	unsigned short	Heading1;
//	unsigned short	Pitch0;
//	unsigned short	Pitch1;
	unsigned short	gpssecond9820;
	unsigned short	gpssecond9821;
	unsigned short	sensorTemp;
	unsigned short	gyroGrp20;
	unsigned short	gyroGrp21;
	unsigned short	velStatus;
	unsigned short	magGrp0;
	unsigned short	magGrp1;
	unsigned short	magGrp2;
	unsigned short	VERSION;   
} fpgadata_t;


extern	int goutputmode;
extern	fpgadata_t	gpagedata;






#define	U4RX_MAXCOUNT		(1024 * 4)
#define	U7RX_MAXCOUNT		(1024 * 4)
#define	FRAMEPARSEBUFSIZE	(512 * 2)
extern	unsigned char grxbuffer[U4RX_MAXCOUNT];
extern	unsigned char grxbuffer3[U4RX_MAXCOUNT];
extern	int grxlen, grxst;
extern	unsigned char grxbuffer7[U7RX_MAXCOUNT];
extern	int grxlen7, grxst7;
extern	unsigned char gframeParsebuf[FRAMEPARSEBUFSIZE];
extern	can_receive_message_struct gCanRxBuf;


extern	void printf_uart4(int type, char *fmt, ...);
extern	void uart4sendmsg(char *txbuf, int size);
extern	void uart6sendmsg422(char *txbuf, int size);
extern	void uart3sendmsg422(char *txbuf, int size);
extern	void uart7sendmsgdebug(char *txbuf, int size);
extern	void uart4sendmsg_billdebug(char *txbuf, int size);
extern	void uart4sendmsg_canout(can_receive_message_struct *receive_message);
extern	void analysisRxdata(void);

#endif /* __FPGAD_H */


