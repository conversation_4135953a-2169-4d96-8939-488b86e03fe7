#include "gd32f4xx.h"
#include "string.h"
#include "stdlib.h"
#include "clock.h"
#include "systick.h"
#include "ymodem.h"
#include "uartadapter.h"
#include "fmc_operation.h"
#include "computerFrameParse.h"
#include "fpgad.h"
#include "lpbus.h"
#include "nav_includes.h"
#include "SetParaBao.h"

VimufTx	gvimuftx, gvimuftxtmp, gvimutxT;
VgpsTx	gvgpstx, gvgpstxtmp, gvgpstxT;
rxonemagnetinfo_t	gtxonemagnetinfo;
VimufIn	gimufIn;
txins320_3b	gtxins3203B;


//const char cimudemodata[26 * 6 + 11] = { //167
//	0x3A, 0x01, 0x00, 0x09, 0x00, 0x9C, 0x00, 0x30, 0x5E, 0x00, 0x00, 0x00, 0x00, 0x88, 0x3B, 0x00, 0x40, 0xDF, 0x3D, 0x00, 0x98, 0x7B, 0xBF, 0x8E, 0xAC, 0xDA, //26
//	0xBB, 0x6D, 0xC6, 0xE2, 0x3D, 0x6B, 0x41, 0x7B, 0xBF, 0x9A, 0x99, 0x99, 0xBD, 0x49, 0x92, 0x8C, 0xBD, 0x9A, 0x99, 0x19, 0xBC, 0xFA, 0x18, 0x9C, 0xBE, 0xFA,
//	0x18, 0x9C, 0xBE, 0x2C, 0x51, 0xBB, 0x3E, 0x70, 0x30, 0xBB, 0xBB, 0xB4, 0x61, 0x07, 0xBC, 0x40, 0x93, 0x68, 0xBA, 0x00, 0xF0, 0xD9, 0x3A, 0x00, 0x5C, 0x19,
//	0xBB, 0x20, 0x0D, 0xA4, 0x3C, 0x46, 0xAB, 0xB9, 0xBB, 0x8A, 0xAC, 0x08, 0xBC, 0x0F, 0x33, 0x5F, 0xBA, 0xFB, 0x22, 0xE2, 0x3A, 0x96, 0x44, 0x12, 0xBB, 0xC3,
//	0x62, 0xA4, 0x3C, 0xCE, 0xCC, 0x5A, 0xC2, 0x56, 0x55, 0xD5, 0x40, 0x56, 0x55, 0xF9, 0xC1, 0xC3, 0x05, 0x36, 0xC1, 0xA3, 0x1B, 0x98, 0x42, 0xC0, 0x1F, 0xA5,
//	0xC1, 0x12, 0x98, 0x7F, 0x3F, 0xD0, 0x47, 0x66, 0x3D, 0xF0, 0x67, 0x3D, 0x3B, 0x00, 0xCD, 0x51, 0x36, 0xE8, 0x44, 0xCE, 0xC0, 0xB4, 0x4E, 0xA9, 0xBE, 0x12,
//	0x9A, 0x95, 0x3C, 0xD1, 0x35, 0xF8, 0x41, 0x8C, 0x4A, 0x0D, 0x0A //11
//};

//const char cimudemodata[103] = { //167
//	0x3A, 0x01, 0x00, 0x09, 0x00, 0x5C, 0x00, 0x3E, 0xD2, 0x05, //1
//	0x00, 0x40, 0x8B, 0x7C, 0x39, 0x80, 0xB8, 0x51, 0xBC, 0x6E,
//	0x91, 0x7B, 0xBF, 0xE0, 0xDF, 0x8D, 0xBB, 0x80, 0x72, 0xB1,
//	0xBA, 0x50, 0x45, 0x8B, 0xBB, 0x00, 0xC0, 0x2B, 0xBC, 0xC0,
//	0x56, 0x49, 0xBB, 0x00, 0x24, 0xA2, 0xBB, 0x21, 0x21, 0x8B,
//	0xBB, 0xD0, 0x55, 0xB3, 0xBA, 0x30, 0x3A, 0x8C, 0xBB, 0xFC,
//	0x2D, 0x2B, 0xBC, 0x9B, 0x4F, 0x47, 0xBB, 0x64, 0xE1, 0xA3, 
//	0xBB, 0xD3, 0xB0, 0x1C, 0xC2, 0x58, 0xFD, 0x86, 0xC1, 0x4B,
//	0x96, 0xB4, 0xC1, 0x46, 0xCE, 0x73, 0x3F, 0xAD, 0x40, 0xBE,
//	0xBB, 0xB8, 0x42, 0xCF, 0xBA, 0x7A, 0x1B, 0x9C, 0xBE, 0x55, //10
//	0x30, 0x0D, 0x0A
//};

const char cimudemodata[103] = { //167
	0x3A, 0x01, 0x00, 0x09, 0x00, 0x5C, 0x00, 0x3E, 0xD2, 0x05,
	0x00, 0x40, 0x8B, 0x7C, 0x39, 0x80, 0xB8, 0x51, 0xBC, 0x6E,
	0x91, 0x7B, 0xBF, 0xE0, 0xDF, 0x8D, 0xBB, 0x80, 0x72, 0xB1,
	0xBA, 0x50, 0x45, 0x8B, 0xBB, 0x00, 0xC0, 0x2B, 0xBC, 0xC0,
	0x56, 0x49, 0xBB, 0x00, 0x24, 0xA2, 0xBB, 0x21, 0x21, 0x8B,
	0xBB, 0xD0, 0x55, 0xB3, 0xBA, 0x30, 0x3A, 0x8C, 0xBB, 0xFC,
	0x2D, 0x2B, 0xBC, 0x9B, 0x4F, 0x47, 0xBB, 0x64, 0xE1, 0xA3, 
	0xBB, 0xD3, 0xB0, 0x1C, 0xC2, 0x58, 0xFD, 0x86, 0xC1, 0x4B,
	0x96, 0xB4, 0xC1, 0x46, 0xCE, 0x73, 0x3F, 0xAD, 0x40, 0xBE,
	0xBB, 0xB8, 0x42, 0xCF, 0xBA, 0x7A, 0x1B, 0x9C, 0xBE, 0x55,
	0x30, 0x0D, 0x0A
};

void imudemodata(void)
{
	memcpy(&gvimuftx, cimudemodata, sizeof(gvimuftx));
	
	int i;
	unsigned short checksum = 0;
	unsigned char *ptmpchar = (unsigned char *)&gvimuftx;
	ptmpchar++;
	for (i = 1; i < sizeof(gvimuftx) - 4; i++) {
		checksum += *(ptmpchar++);
	}
	gvimuftx.tail.checksum = checksum;
}

int gVgpsOutTimes = 0;
char goutbuf00[1024];
char goutbuf01[1024];
int timestamp = 0;
#if 0
void NAV_out(void)
{
	gVgpsOutTimes++;
	if (1) {
		if (0) {
			uart4sendmsg((char*)&gvimuftx, sizeof(gvimuftx));
		}
		else {
			gtxonemagnetinfo.head0 = 0xaa;
			gtxonemagnetinfo.head1 = 0x55;
			gtxonemagnetinfo.len = sizeof(rxonemagnetinfo_t);
			gtxonemagnetinfo.type = 5;      //data type  1: imu     2: cacv     3: pps      4:gnss    5:magnet
			gtxonemagnetinfo.checksum = 0;
			gtxonemagnetinfo.magnetdata = gvimuftx;
			gtxonemagnetinfo.tt = gVgpsOutTimes * 5000.0;
			gtxonemagnetinfo.packet = gVgpsOutTimes;

			unsigned char *ptmpu8;
			unsigned short checksum;
			ptmpu8 = (unsigned char *)&gtxonemagnetinfo;
			checksum = 0;

			__disable_irq();    //----------------------------------
			gtxonemagnetinfo.packetT = gVgpsOutTimes;	//gdriverspacket;  //total packet index
			for (int i = 0; i < sizeof(rxonemagnetinfo_t) - 2; i++) {
				checksum += *ptmpu8++;
			}
			gtxonemagnetinfo.checksum = checksum;
			__enable_irq();    //----------------------------------
			
			uart4sendmsg((char*)&gtxonemagnetinfo, sizeof(gtxonemagnetinfo));
		}
	}
	else {
		Vimuf	*pVD = &gvimuftx.imuD;
		printf_uart4(0, "----gVgps outputs The Times: %d\r\n", gVgpsOutTimes);
//typedef struct _Vimuf {
//	UInt32	 timestamp;
		printf_uart4(0, "                  timestamp: %d.\r\n", pVD->timestamp);
//	Vector3f OriginalAcceleration;
		printf_uart4(0, "       OriginalAcceleration: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->OriginalAcceleration.x, pVD->OriginalAcceleration.y, pVD->OriginalAcceleration.z);
//	Vector3f CelebrationAcceleration;	//3
		printf_uart4(0, "    CelebrationAcceleration: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->CelebrationAcceleration.x, pVD->CelebrationAcceleration.y, pVD->CelebrationAcceleration.z);
//	Vector3f OriginalGyrol;
		printf_uart4(0, "              OriginalGyrol: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->OriginalGyrol.x, pVD->OriginalGyrol.y, pVD->OriginalGyrol.z);
//	Vector3f OriginalGyroll;
		printf_uart4(0, "             OriginalGyroll: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->OriginalGyroll.x, pVD->OriginalGyroll.y, pVD->OriginalGyroll.z);
//	Vector3f StaticCelebrationGyrol;
		printf_uart4(0, "     StaticCelebrationGyrol: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->StaticCelebrationGyrol.x, pVD->StaticCelebrationGyrol.y, pVD->StaticCelebrationGyrol.z);
//	Vector3f StaticCelebrationGyroll;
		printf_uart4(0, "    StaticCelebrationGyroll: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->StaticCelebrationGyroll.x, pVD->StaticCelebrationGyroll.y, pVD->StaticCelebrationGyroll.z);
//	Vector3f CoordinateCelebrationGyrol;
		printf_uart4(0, " CoordinateCelebrationGyrol: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->CoordinateCelebrationGyrol.x, pVD->CoordinateCelebrationGyrol.y, pVD->CoordinateCelebrationGyrol.z);
//	Vector3f CoordinateCelebrationGyroll;	//9
		printf_uart4(0, "CoordinateCelebrationGyroll: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->CoordinateCelebrationGyroll.x, pVD->CoordinateCelebrationGyroll.y, pVD->CoordinateCelebrationGyroll.z);
//	Vector3f OriginalMagnetometer;
		printf_uart4(0, "       OriginalMagnetometer: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->OriginalMagnetometer.x, pVD->OriginalMagnetometer.y, pVD->OriginalMagnetometer.z);
//	Vector3f CelebrationMagnetometer;	//11
		printf_uart4(0, "    CelebrationMagnetometer: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->CelebrationMagnetometer.x, pVD->CelebrationMagnetometer.y, pVD->CelebrationMagnetometer.z);
//	//Vector3f Angularvelocity;
		printf_uart4(0, "            Angularvelocity: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->Angularvelocity.x, pVD->Angularvelocity.y, pVD->Angularvelocity.z);
//	Vector4f Quaternion;
		printf_uart4(0, "                 Quaternion: w: %+.9f x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->Quaternion.w, pVD->Quaternion.x, pVD->Quaternion.y, pVD->Quaternion.z);
//	Vector3f Eulerangle;
		printf_uart4(0, "                 Eulerangle: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->Eulerangle.x, pVD->Eulerangle.y, pVD->Eulerangle.z);
//	Vector3f LinearAcceleration;
		printf_uart4(0, "         LinearAcceleration: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->LinearAcceleration.x, pVD->LinearAcceleration.y, pVD->LinearAcceleration.z);
//	Float32 Reserve0;
//	Float32 Reserve1;
		printf_uart4(0, "                   Reserve0: %f\r\n", pVD->Reserve0);
		printf_uart4(0, "                   Reserve1: %f\r\n", pVD->Reserve1);
//	Float32 Temperature;
		printf_uart4(0, "                Temperature: %.9f.\r\n\r\n\r\n", pVD->Temperature);
//} Vimuf;
		
	}
}
#else
void test()
{
	sprintf(goutbuf00, "%f %f %f %f %f %f %f %f %f %f %f %f %f %d \r\n", NAV_Data_Full.IMU.acc_use[0],NAV_Data_Full.IMU.acc_use[1],NAV_Data_Full.IMU.acc_use[2],
	NAV_Data_Full.IMU.gyro_use[0],NAV_Data_Full.IMU.gyro_use[1],NAV_Data_Full.IMU.gyro_use[2],NAV_Data_Full.MAGNET.mag_raw[0],NAV_Data_Full.MAGNET.mag_raw[1],NAV_Data_Full.MAGNET.mag_raw[2],
	NAV_Data_Full.MAGNET.mag_use[0],NAV_Data_Full.MAGNET.mag_use[1],NAV_Data_Full.MAGNET.mag_use[2],NAV_Data_Full.MAGNET.mag_angle,NAV_Data_Full.mag_cali_flag);
	uart3sendmsg422(goutbuf00, strlen(goutbuf00));
}

void MagCaliDisplay()
{
	static int temp = 0;
	
	if(temp != NAV_Data_Full.magbuff_info.index)
	{
		sprintf(goutbuf00,"Magnet Cali Processing:%d%% \r\n",NAV_Data_Full.magbuff_info.index);
	uart3sendmsg422(goutbuf00, strlen(goutbuf00));
	}
	temp = NAV_Data_Full.magbuff_info.index;
	if(temp == MAG_BUFFER_SIZE)
	{
		MagInitCali(&NAV_Data_Full);
		memset(goutbuf00,0,sizeof(goutbuf00));
		sprintf(goutbuf00,"Magnet Cali Processed:%f %f %f %f %f %f ,please reboot!\r\n",NAV_Data_Full.MAGNET_BUFF.Magoff[0],NAV_Data_Full.MAGNET_BUFF.Magoff[1],NAV_Data_Full.MAGNET_BUFF.Magoff[2],
		NAV_Data_Full.MAGNET_BUFF.Magoff[3],NAV_Data_Full.MAGNET_BUFF.Magoff[4],NAV_Data_Full.MAGNET_BUFF.Magoff[5]);
		uart3sendmsg422(goutbuf00, strlen(goutbuf00));
	}
}

void NAV_out(void)
{
	gVgpsOutTimes++;
    static uint16_t sendCnt = 0;
    uint16_t freq = (SETPARA_DATAOUT_FPGA_FREQ / hSetting.settingData.freq);
    sendCnt++;

    if(sendCnt >= freq)
    {
        sendCnt = 0;
        if(0 == combineData.outputType)
        {
	
    if (1) {
      if (goutputmode == c_outputmode_normal) {
#if c_potocol_ins320_3A
			//if (gVgpsOutTimes % 2) {
				gvimuftx.head.head = 0x3a;
				gvimuftx.head.sensorid = 0x01;
				gvimuftx.head.cmd = 0x09;
				gvimuftx.head.len = sizeof(gvimuftx.imuD);
				timestamp += 10;
				gvimuftx.imuD.timestamp = timestamp;
				int i;
				unsigned short checksum = 0;
				unsigned char *ptmpchar = (unsigned char *)&gvimuftx;
				ptmpchar++;
				for (i = 1; i < sizeof(gvimuftx) - 4; i++) {
					checksum += *(ptmpchar++);
				}
				gvimuftx.tail.checksum = checksum;
				gvimuftx.tail.fmt0a = 0x0a;
				gvimuftx.tail.fmt0d = 0x0d;
				uart4sendmsg((char*)&gvimuftx, sizeof(gvimuftx));
			//}
#endif
#if c_potocol_ins320_3B
			gtxins3203B.head00xbd = 0xbd;
			gtxins3203B.head10xdb = 0xdb;
			gtxins3203B.imutype0x0b = 0x0b;
			int i;
			unsigned char checksum = 0;
			unsigned char *ptmpchar = (unsigned char *)&gtxins3203B;
			for (i = 0; i < sizeof(gtxins3203B) - 1; i++) {
				checksum ^= *(ptmpchar++);
			}
			gtxins3203B.checksum = checksum;
			uart4sendmsg((char*)&gtxins3203B, sizeof(gtxins3203B));
#endif
		}
		else if (goutputmode == c_outputmode_magnet) {
			gtxonemagnetinfo.head0 = 0xaa;
			gtxonemagnetinfo.head1 = 0x55;
			gtxonemagnetinfo.len = sizeof(rxonemagnetinfo_t);
			gtxonemagnetinfo.type = 5;      //data type  1: imu     2: cacv     3: pps      4:gnss    5:magnet
			gtxonemagnetinfo.checksum = 0;
			gtxonemagnetinfo.magnetdata = gimufIn;
			gtxonemagnetinfo.tt = gVgpsOutTimes * 5000.0;
			gtxonemagnetinfo.packet = gVgpsOutTimes;

			unsigned char *ptmpu8;
			unsigned short checksum;
			ptmpu8 = (unsigned char *)&gtxonemagnetinfo;
			checksum = 0;

			__disable_irq();    //----------------------------------
			gtxonemagnetinfo.packetT = gVgpsOutTimes;	//gdriverspacket;  //total packet index
			for (int i = 0; i < sizeof(rxonemagnetinfo_t) - 2; i++) {
				checksum += *ptmpu8++;
			}
			gtxonemagnetinfo.checksum = checksum;
			__enable_irq();    //----------------------------------
			
			uart4sendmsg((char*)&gtxonemagnetinfo, sizeof(gtxonemagnetinfo));
		}
		else if (goutputmode == mag_calimodel)// magnet calibrate info
		{
			MagCaliDisplay();//
		}
	}
	else {
		Vimuf	*pVD = &gvimuftx.imuD;
//		printf_uart4(0, "----gVgps outputs The Times: %d\r\n", gVgpsOutTimes);
//		sprintf(goutbuf00, "----gVgps outputs The Times: %d\r\n", gVgpsOutTimes);
//		printf_uart4(0, "                  timestamp: %d.\r\n", pVD->timestamp);
//		sprintf(goutbuf01, "                  timestamp: %d.\r\n", pVD->timestamp);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "       OriginalAcceleration: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->OriginalAcceleration.x, pVD->OriginalAcceleration.y, pVD->OriginalAcceleration.z);
//		sprintf(goutbuf01, "       OriginalAcceleration: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->OriginalAcceleration.x, pVD->OriginalAcceleration.y, pVD->OriginalAcceleration.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "    CelebrationAcceleration: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->CelebrationAcceleration.x, pVD->CelebrationAcceleration.y, pVD->CelebrationAcceleration.z);
//		sprintf(goutbuf01, "    CelebrationAcceleration: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->CelebrationAcceleration.x, pVD->CelebrationAcceleration.y, pVD->CelebrationAcceleration.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "              OriginalGyrol: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->OriginalGyrol.x, pVD->OriginalGyrol.y, pVD->OriginalGyrol.z);
//		sprintf(goutbuf01, "              OriginalGyrol: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->OriginalGyrol.x, pVD->OriginalGyrol.y, pVD->OriginalGyrol.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "             OriginalGyroll: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->OriginalGyroll.x, pVD->OriginalGyroll.y, pVD->OriginalGyroll.z);
//		sprintf(goutbuf01, "             OriginalGyroll: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->OriginalGyroll.x, pVD->OriginalGyroll.y, pVD->OriginalGyroll.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "     StaticCelebrationGyrol: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->StaticCelebrationGyrol.x, pVD->StaticCelebrationGyrol.y, pVD->StaticCelebrationGyrol.z);
//		sprintf(goutbuf01, "     StaticCelebrationGyrol: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->StaticCelebrationGyrol.x, pVD->StaticCelebrationGyrol.y, pVD->StaticCelebrationGyrol.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "    StaticCelebrationGyroll: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->StaticCelebrationGyroll.x, pVD->StaticCelebrationGyroll.y, pVD->StaticCelebrationGyroll.z);
//		sprintf(goutbuf01, "    StaticCelebrationGyroll: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->StaticCelebrationGyroll.x, pVD->StaticCelebrationGyroll.y, pVD->StaticCelebrationGyroll.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, " CoordinateCelebrationGyrol: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->CoordinateCelebrationGyrol.x, pVD->CoordinateCelebrationGyrol.y, pVD->CoordinateCelebrationGyrol.z);
//		sprintf(goutbuf01, " CoordinateCelebrationGyrol: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->CoordinateCelebrationGyrol.x, pVD->CoordinateCelebrationGyrol.y, pVD->CoordinateCelebrationGyrol.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "CoordinateCelebrationGyroll: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->CoordinateCelebrationGyroll.x, pVD->CoordinateCelebrationGyroll.y, pVD->CoordinateCelebrationGyroll.z);
//		sprintf(goutbuf01, "CoordinateCelebrationGyroll: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->CoordinateCelebrationGyroll.x, pVD->CoordinateCelebrationGyroll.y, pVD->CoordinateCelebrationGyroll.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "       OriginalMagnetometer: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->OriginalMagnetometer.x, pVD->OriginalMagnetometer.y, pVD->OriginalMagnetometer.z);
//		sprintf(goutbuf01, "       OriginalMagnetometer: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->OriginalMagnetometer.x, pVD->OriginalMagnetometer.y, pVD->OriginalMagnetometer.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "    CelebrationMagnetometer: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->CelebrationMagnetometer.x, pVD->CelebrationMagnetometer.y, pVD->CelebrationMagnetometer.z);
//		sprintf(goutbuf01, "    CelebrationMagnetometer: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->CelebrationMagnetometer.x, pVD->CelebrationMagnetometer.y, pVD->CelebrationMagnetometer.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "            Angularvelocity: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->Angularvelocity.x, pVD->Angularvelocity.y, pVD->Angularvelocity.z);
//		sprintf(goutbuf01, "            Angularvelocity: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->Angularvelocity.x, pVD->Angularvelocity.y, pVD->Angularvelocity.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "                 Quaternion: w: %+.9f x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->Quaternion.w, pVD->Quaternion.x, pVD->Quaternion.y, pVD->Quaternion.z);
//		sprintf(goutbuf01, "                 Quaternion: w: %+.9f x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->Quaternion.w, pVD->Quaternion.x, pVD->Quaternion.y, pVD->Quaternion.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "                 Eulerangle: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->Eulerangle.x, pVD->Eulerangle.y, pVD->Eulerangle.z);
//		sprintf(goutbuf01, "                 Eulerangle: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->Eulerangle.x, pVD->Eulerangle.y, pVD->Eulerangle.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "         LinearAcceleration: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->LinearAcceleration.x, pVD->LinearAcceleration.y, pVD->LinearAcceleration.z);
//		sprintf(goutbuf01, "         LinearAcceleration: x: %+.9f y: %+.9f z: %+.9f\r\n", pVD->LinearAcceleration.x, pVD->LinearAcceleration.y, pVD->LinearAcceleration.z);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "                   Reserve0: %f\r\n", pVD->Reserve0);
//		sprintf(goutbuf01, "                   Reserve0: %f\r\n", pVD->Reserve0);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "                   Reserve1: %f\r\n", pVD->Reserve1);
//		sprintf(goutbuf01, "                   Reserve1: %f\r\n", pVD->Reserve1);
//		strcat(goutbuf00, goutbuf01);
//		printf_uart4(0, "                Temperature: %.9f.\r\n\r\n\r\n", pVD->Temperature);
//		sprintf(goutbuf01, "                Temperature: %.9f.\r\n\r\n\r\n", pVD->Temperature);
//		strcat(goutbuf00, goutbuf01);

            sprintf(goutbuf00, "%f %f %f %f %f %f %f %f %f %f \r\n", pVD->LinearAcceleration.x, pVD->LinearAcceleration.y, pVD->LinearAcceleration.z, pVD->Angularvelocity.x,pVD->Angularvelocity.y,pVD->Angularvelocity.z,\
            pVD->Quaternion.w,pVD->Quaternion.x,pVD->Quaternion.y,pVD->Quaternion.z);
            uart3sendmsg422(goutbuf00, strlen(goutbuf00));
        }
      }
    }
}
#endif


#pragma pack()

