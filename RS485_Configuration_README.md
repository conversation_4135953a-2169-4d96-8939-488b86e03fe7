# RS485配置说明

## 概述
本项目已成功添加了RS485输出接口支持，可以通过宏定义在RS422和RS485接口之间切换。

**重要说明**：RS422和RS485使用完全不同的数据路径：
- **RS422模式**：数据通过FPGA UART输出（PA0/PA1连接到FPGA）
- **RS485模式**：数据通过MCU USART0直接输出（PA9/PA10直接连接到MCU）

## 配置方法

### 1. 接口选择
在 `Protocol/config.h` 文件中，通过修改以下宏定义来选择输出接口：

```c
// 选择输出接口：0=RS422, 1=RS485
#define OUTPUT_INTERFACE_SELECT         OUTPUT_INTERFACE_RS485
```

### 2. 接口对比

| 接口类型 | 串口 | 引脚 | 波特率 | 数据路径 | 模式 |
|---------|------|------|--------|----------|------|
| RS422 | FPGA UART | PA0(TX), PA1(RX) | 460800 | FPGA→PA0/PA1 | UART_RS422 |
| RS485 | MCU USART0 | PA9(TX), PA10(RX) | 460800 | MCU→PA9/PA10 | 直接输出 |

### 3. 修改的文件

#### Protocol/config.h
- 添加了接口选择宏定义
- `OUTPUT_INTERFACE_SELECT` 控制接口选择

#### Protocol/UartDefine.h 和 bsp/inc/bsp_fmc.h
- 添加了 `UART_TXPORT_COMPLEX_0` 和 `UART_RXPORT_COMPLEX_0` 定义
- 对应USART0接口

#### Protocol/computerFrameParse.c
- 修改了UART初始化代码
- 根据宏定义选择不同的端口和模式

#### Source/src/main.c
- 添加了USART0的中断配置
- 当选择RS485时，初始化USART0

#### bsp/src/bsp_uart.c
- 添加了USART0中断处理函数
- 修改了波特率为460800

## 使用步骤

### 切换到RS485接口：
1. 打开 `Protocol/config.h`
2. 确保以下设置：
   ```c
   #define OUTPUT_INTERFACE_SELECT         OUTPUT_INTERFACE_RS485
   ```
3. 重新编译项目

### 切换回RS422接口：
1. 打开 `Protocol/config.h`
2. 修改为：
   ```c
   #define OUTPUT_INTERFACE_SELECT         OUTPUT_INTERFACE_RS422
   ```
3. 重新编译项目

## 技术细节

### 数据路径架构
- **RS422模式**：MCU → EXMC接口 → FPGA UART → PA0/PA1引脚
- **RS485模式**：MCU → USART0外设 → PA9/PA10引脚

### FPGA端口映射（仅RS422模式）
- COMPLEX_8 对应 FPGA内部UART，连接到PA0/PA1

### 中断处理
- RS485模式下，USART0_IRQHandler 处理中断
- RS422模式下，数据通过FPGA处理，无需MCU中断

### 波特率设置
- 两种接口都使用460800波特率
- iPMV协议使用230400波特率

### 发送函数修改
所有数据发送函数都已修改为支持双路径：
- `frame_pack_and_send()` - 主要数据发送函数
- `comm_send_end_frame()` - 命令结束帧
- `comm_write_rsp()` - 命令响应帧
- `IMU_test_data_send()` - 测试数据发送
- `Arm_SendMsg()` - ARM调试信息发送

## 注意事项

1. **硬件连接**：确保硬件上RS485收发器正确连接到PA9/PA10引脚
2. **FPGA配置**：确保FPGA固件支持COMPLEX_0端口的RS485模式
3. **编译**：修改配置后需要重新编译整个项目
4. **测试**：建议使用示波器或逻辑分析仪验证信号输出

## 测试验证

### 1. 配置验证
可以使用提供的 `test_rs485_config.c` 文件来验证配置是否正确：

```bash
gcc -I Protocol -I bsp/inc test_rs485_config.c -o test_config
./test_config
```

这将显示当前的配置选择和相应的初始化参数。

### 2. 编译检查
运行 `compile_test.bat` 脚本来检查所有修改的文件是否存在且配置正确：

```cmd
compile_test.bat
```

### 3. 实际测试步骤
1. 确保硬件连接正确（RS485收发器连接到PA9/PA10）
2. 编译并下载程序到目标板
3. 使用串口调试工具或示波器验证PA9/PA10上的RS485信号
4. 检查数据传输是否正常

## 完成的修改总结

### 文件修改列表：
1. **Protocol/config.h** - 添加接口选择宏定义
2. **Protocol/UartDefine.h** - 添加COMPLEX_0端口定义
3. **bsp/inc/bsp_fmc.h** - 添加COMPLEX_0端口定义
4. **Protocol/computerFrameParse.c** - 修改UART初始化逻辑
5. **Source/src/main.c** - 添加USART0初始化和中断配置
6. **bsp/src/bsp_uart.c** - 添加USART0中断处理函数
7. **bsp/inc/bsp_uart.h** - 添加函数声明

### 新增文件：
1. **test_rs485_config.c** - 配置测试程序
2. **compile_test.bat** - 编译检查脚本
3. **RS485_Configuration_README.md** - 本说明文档

所有修改都已完成，项目现在支持通过宏定义在RS422和RS485接口之间切换。
