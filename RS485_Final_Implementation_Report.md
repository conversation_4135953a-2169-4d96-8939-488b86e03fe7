# RS485接口实现完整报告

## 项目概述
本项目成功实现了GD32F470xx硬件平台上的RS485接口支持，通过宏定义实现RS422和RS485接口的灵活切换。

## 架构理解与修正

### 原始架构分析
- **RS422模式**：使用FPGA UART系统，PA0/PA1连接到FPGA，通过EXMC接口(0x60000000)访问FPGA UART端口COMPLEX_8
- **RS485需求**：PA9/PA10为MCU USART0引脚，**不连接FPGA**，需要直接使用MCU USART0

### 关键架构发现
经过用户纠正，发现了重要的架构事实：
- PA0/PA1连接到FPGA，不是直接连接MCU UART3
- PA9/PA10是MCU USART0引脚，**没有连接到FPGA**
- 因此RS485模式需要完全绕过FPGA，直接使用MCU USART0

### 双路径数据架构
实现了两种完全不同的数据输出路径：
1. **RS422路径**：数据 → FPGA UART系统 → PA0/PA1
2. **RS485路径**：数据 → MCU USART0 → PA9/PA10

## 实现方案

### 1. 配置宏定义
在 `Protocol/config.h` 中添加：
```c
#define OUTPUT_INTERFACE_RS422          0       // RS422输出接口 (UART3, PA0/PA1)
#define OUTPUT_INTERFACE_RS485          1       // RS485输出接口 (USART0, PA9/PA10)
#define OUTPUT_INTERFACE_SELECT         OUTPUT_INTERFACE_RS485
```

### 2. RS485发送函数
在 `bsp/src/bsp_uart.c` 中实现：
```c
void RS485_SendData(uint8_t* data, uint16_t len)
{
    for(uint16_t i = 0; i < len; i++) {
        usart_data_transmit(USART0, data[i]);
        while(RESET == usart_flag_get(USART0, USART_FLAG_TC));
    }
}
```

### 3. 条件编译模式
所有数据发送函数都采用以下模式：
```c
#if OUTPUT_INTERFACE_SELECT == OUTPUT_INTERFACE_RS485
    // RS485模式：使用MCU USART0直接发送
    RS485_SendData((uint8_t*)data, len);
#else
    // RS422模式：使用FPGA UART发送
    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, data);
#endif
```

## 修改的文件清单

### 核心配置文件
1. **Protocol/config.h** - 添加接口选择宏定义

### 数据发送函数修改
2. **Protocol/frame_analysis.c**
   - `frame_pack_and_send()` - 主要数据发送
   - `frame_navi_and_gnss_send()` - 导航测试数据发送

3. **Protocol/computerFrameParse.c**
   - `comm_send_end_frame()` - 命令结束帧
   - `comm_write_rsp()` - 命令响应帧
   - `IMU_test_data_send()` - IMU测试数据
   - UART初始化逻辑（RS485模式跳过FPGA初始化）

4. **NAV/nav_cli.c**
   - `Arm_SendMsg()` - ARM调试信息发送

5. **Source/src/ymodem.c**
   - `ins_device_write()` - Ymodem协议数据发送

6. **Source/src/gd32f4xx_it.c**
   - `uart3sendmsg422()` - UART3调试数据发送

### 硬件抽象层
7. **bsp/src/bsp_uart.c**
   - 添加 `RS485_SendData()` 函数
   - 添加 `USART0_IRQHandler()` 中断处理

8. **bsp/inc/bsp_uart.h**
   - 添加 `RS485_SendData()` 函数声明

### 初始化配置
9. **Source/src/main.c**
   - 添加USART0初始化和中断配置

## 错误修正记录

### 删除的错误定义
- ~~`UART_TXPORT_COMPLEX_0`~~ - PA9/PA10不连接FPGA
- ~~`UART_RXPORT_COMPLEX_0`~~ - PA9/PA10不连接FPGA

这些定义基于错误的架构理解，已被完全移除。

## 技术特性

### 接口对比
| 特性 | RS422模式 | RS485模式 |
|------|-----------|-----------|
| 数据路径 | MCU → FPGA → PA0/PA1 | MCU → USART0 → PA9/PA10 |
| 串口 | FPGA UART (COMPLEX_8) | MCU USART0 |
| 引脚 | PA0(TX), PA1(RX) | PA9(TX), PA10(RX) |
| 波特率 | 460800 | 460800 |
| 中断处理 | FPGA UART中断 | USART0_IRQHandler |
| FPGA依赖 | 需要FPGA | 绕过FPGA |

### 切换方法
1. 修改 `Protocol/config.h` 中的 `OUTPUT_INTERFACE_SELECT`
2. 重新编译项目
3. 烧录到硬件

## 验证状态

### 代码完整性
- ✅ 所有数据发送函数已修改
- ✅ 条件编译语法正确
- ✅ 头文件依赖完整
- ✅ 函数声明和定义匹配

### 架构正确性
- ✅ 正确理解FPGA架构
- ✅ 删除错误的FPGA映射
- ✅ 实现正确的双路径架构
- ✅ RS485模式完全绕过FPGA

## 使用说明

### 启用RS485接口
```c
// 在 Protocol/config.h 中设置
#define OUTPUT_INTERFACE_SELECT OUTPUT_INTERFACE_RS485
```

### 启用RS422接口
```c
// 在 Protocol/config.h 中设置
#define OUTPUT_INTERFACE_SELECT OUTPUT_INTERFACE_RS422
```

## 总结
本实现成功解决了原始需求，提供了一个完整的、架构正确的RS485接口解决方案。通过宏定义实现了灵活的接口切换，同时保持了代码的可维护性和扩展性。关键的架构理解修正确保了实现的正确性和可靠性。
