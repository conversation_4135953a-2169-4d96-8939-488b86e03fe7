# RS485接口实现修正报告

## 架构理解修正

### 之前的错误理解
- 错误地认为RS422通过FPGA UART输出
- 使用了错误的`Uart_SendMsg(UART_TXPORT_COMPLEX_8, ...)`调用

### 正确的架构理解
- **RS422模式**：MCU UART3 (PA0/PA1) → 直接输出给上位机，波特率460800
- **RS485模式**：MCU USART0 (PA9/PA10) → 直接输出给上位机，波特率460800
- 两种模式都是MCU直接输出，不经过FPGA

## 修正的实现方案

### 1. 配置宏定义（保持不变）
在 `Protocol/config.h` 中：
```c
#define OUTPUT_INTERFACE_RS422          0       // RS422输出接口 (UART3, PA0/PA1)
#define OUTPUT_INTERFACE_RS485          1       // RS485输出接口 (USART0, PA9/PA10)
#define OUTPUT_INTERFACE_SELECT         OUTPUT_INTERFACE_RS485
```

### 2. 硬件初始化（已正确配置）
- **UART3初始化**：在`main.c`的`gd_eval_com_init()`中，PA0/PA1配置为UART3，波特率460800
- **USART0初始化**：在`main.c`中通过`bsp_systick_init(USART0)`配置PA9/PA10，波特率460800
- **中断配置**：UART3_IRQn和USART0_IRQn都已正确配置

### 3. 数据发送函数修正

#### RS485发送函数（保持不变）
在 `bsp/src/bsp_uart.c` 中：
```c
void RS485_SendData(uint8_t* data, uint16_t len)
{
    for(uint16_t i = 0; i < len; i++) {
        usart_data_transmit(USART0, data[i]);
        while(RESET == usart_flag_get(USART0, USART_FLAG_TC));
    }
}
```

#### 条件编译模式修正
所有数据发送函数都修正为：
```c
#if OUTPUT_INTERFACE_SELECT == OUTPUT_INTERFACE_RS485
    // RS485模式：使用MCU USART0直接发送
    RS485_SendData((uint8_t*)data, len);
#else
    // RS422模式：使用MCU UART3直接发送
    uart3sendmsg422((char*)data, len);
#endif
```

### 4. 修正的文件清单

#### 数据发送函数修正
1. **Protocol/frame_analysis.c**
   - `frame_pack_and_send()` - 修正为使用uart3sendmsg422()
   - `frame_navi_and_gnss_send()` - 修正为使用uart3sendmsg422()

2. **Protocol/computerFrameParse.c**
   - `comm_send_end_frame()` - 修正为使用uart3sendmsg422()
   - `comm_write_rsp()` - 修正为使用uart3sendmsg422()
   - `IMU_test_data_send()` - 修正为使用uart3sendmsg422()
   - UART初始化逻辑注释修正

3. **NAV/nav_cli.c**
   - `Arm_SendMsg()` - 修正为使用uart3sendmsg422()

4. **Source/src/ymodem.c**
   - `ins_device_write()` - 修正为使用uart3sendmsg422()

5. **Source/src/gd32f4xx_it.c**
   - `uart3sendmsg422()` - 注释修正，逻辑保持不变

### 5. UART3发送机制
UART3的发送通过中断方式实现：
- `uart3sendmsg422()`函数将数据复制到`tx_buffer`
- 启用UART3的TBE中断
- `UART3_IRQHandler()`中断处理函数逐字节发送数据
- 发送完成后设置`gbtxcompleted = 1`

### 6. 数据接收处理
- **RS422模式**：通过UART3_IRQHandler接收数据到grxbuffer
- **RS485模式**：通过USART0_IRQHandler接收数据到相同的grxbuffer
- 两种模式使用相同的数据处理逻辑

## 使用方法

### 切换到RS485接口：
1. 打开 `Protocol/config.h`
2. 设置：`#define OUTPUT_INTERFACE_SELECT OUTPUT_INTERFACE_RS485`
3. 重新编译项目
4. 数据将通过PA9/PA10 (USART0) 输出

### 切换回RS422接口：
1. 打开 `Protocol/config.h`
2. 设置：`#define OUTPUT_INTERFACE_SELECT OUTPUT_INTERFACE_RS422`
3. 重新编译项目
4. 数据将通过PA0/PA1 (UART3) 输出

## 总结
修正后的实现正确反映了硬件架构：
- RS422和RS485都是MCU直接输出，不经过FPGA
- 通过宏定义实现两种接口的无缝切换
- 保持了原有的数据处理逻辑和中断机制
- 所有数据发送点都已正确修正
