/*
 * 宏定义测试文件
 * 用于验证OUTPUT_INTERFACE_SELECT宏是否正确定义
 */

#include "config.h"

// 预处理器测试
#if OUTPUT_INTERFACE_SELECT == OUTPUT_INTERFACE_RS485
    #pragma message("宏定义正确：OUTPUT_INTERFACE_SELECT == OUTPUT_INTERFACE_RS485")
    int test_rs485_enabled = 1;
#else
    #pragma message("宏定义错误：OUTPUT_INTERFACE_SELECT != OUTPUT_INTERFACE_RS485")
    int test_rs485_enabled = 0;
#endif

// 显示宏的实际值
#define XSTR(s) #s
#define XSTR2(s) XSTR(s)
#pragma message("OUTPUT_INTERFACE_SELECT = " XSTR2(OUTPUT_INTERFACE_SELECT))
#pragma message("OUTPUT_INTERFACE_RS485 = " XSTR2(OUTPUT_INTERFACE_RS485))
#pragma message("OUTPUT_INTERFACE_RS422 = " XSTR2(OUTPUT_INTERFACE_RS422))

void test_macro_values(void)
{
    volatile int select = OUTPUT_INTERFACE_SELECT;
    volatile int rs485 = OUTPUT_INTERFACE_RS485;
    volatile int rs422 = OUTPUT_INTERFACE_RS422;
    
    // 在调试器中查看这些值
    (void)select;
    (void)rs485;
    (void)rs422;
}
