@echo off
echo ========================================
echo RS485配置编译测试脚本
echo ========================================

echo.
echo 1. 检查配置文件...
if exist "Protocol\config.h" (
    echo [OK] config.h 存在
    findstr "OUTPUT_INTERFACE_SELECT" Protocol\config.h
) else (
    echo [ERROR] config.h 不存在
    goto :error
)

echo.
echo 2. 检查UartDefine.h修改...
if exist "Protocol\UartDefine.h" (
    echo [OK] UartDefine.h 存在
    findstr "UART_TXPORT_COMPLEX_0" Protocol\UartDefine.h
    findstr "UART_RXPORT_COMPLEX_0" Protocol\UartDefine.h
) else (
    echo [ERROR] UartDefine.h 不存在
    goto :error
)

echo.
echo 3. 检查bsp_fmc.h修改...
if exist "bsp\inc\bsp_fmc.h" (
    echo [OK] bsp_fmc.h 存在
    findstr "UART_TXPORT_COMPLEX_0" bsp\inc\bsp_fmc.h
) else (
    echo [ERROR] bsp_fmc.h 不存在
    goto :error
)

echo.
echo 4. 检查computerFrameParse.c修改...
if exist "Protocol\computerFrameParse.c" (
    echo [OK] computerFrameParse.c 存在
    findstr "OUTPUT_INTERFACE_SELECT" Protocol\computerFrameParse.c
) else (
    echo [ERROR] computerFrameParse.c 不存在
    goto :error
)

echo.
echo 5. 检查main.c修改...
if exist "Source\src\main.c" (
    echo [OK] main.c 存在
    findstr "OUTPUT_INTERFACE_SELECT" Source\src\main.c
) else (
    echo [ERROR] main.c 不存在
    goto :error
)

echo.
echo 6. 检查bsp_uart.c修改...
if exist "bsp\src\bsp_uart.c" (
    echo [OK] bsp_uart.c 存在
    findstr "USART0_IRQHandler" bsp\src\bsp_uart.c
) else (
    echo [ERROR] bsp_uart.c 不存在
    goto :error
)

echo.
echo ========================================
echo 所有文件检查完成！
echo ========================================
echo.
echo 当前配置:
findstr "OUTPUT_INTERFACE_SELECT.*OUTPUT_INTERFACE" Protocol\config.h
echo.
echo 如需切换接口，请修改 Protocol\config.h 中的 OUTPUT_INTERFACE_SELECT 宏定义
echo - OUTPUT_INTERFACE_RS422 = RS422接口 (UART3, PA0/PA1)
echo - OUTPUT_INTERFACE_RS485 = RS485接口 (USART0, PA9/PA10)
echo.
echo 修改后请重新编译整个项目。
goto :end

:error
echo.
echo [ERROR] 检查失败，请确认文件是否存在或修改是否正确。
pause
exit /b 1

:end
echo.
echo 按任意键退出...
pause >nul
