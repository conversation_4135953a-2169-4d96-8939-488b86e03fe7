# RS485接口实现完成报告

## 项目概述
成功为INS600M-31A项目添加了RS485输出接口支持，实现了通过宏定义在RS422和RS485接口之间切换的功能。

## 实现目标
- ✅ 将输出接口从RS422改为RS485
- ✅ 使用USART0替代UART3
- ✅ 配置PA9/PA10引脚替代PA0/PA1
- ✅ 保持460800波特率不变
- ✅ 实现宏定义控制的接口选择机制

## 技术实现详情

### 1. 宏定义配置系统
在 `Protocol/config.h` 中添加：
```c
#define OUTPUT_INTERFACE_RS422          0       // RS422输出接口 (UART3, PA0/PA1)
#define OUTPUT_INTERFACE_RS485          1       // RS485输出接口 (USART0, PA9/PA10)
#define OUTPUT_INTERFACE_SELECT         OUTPUT_INTERFACE_RS485
```

### 2. FPGA端口映射
添加了COMPLEX_0端口定义，对应USART0接口：
- `UART_TXPORT_COMPLEX_0 = 0` (对应USART0 PA9/PA10)
- `UART_RXPORT_COMPLEX_0 = 0` (对应USART0 PA9/PA10)

### 3. 条件编译逻辑
在 `Protocol/computerFrameParse.c` 中实现了双层条件编译：
- 第一层：协议选择 (iPMV vs asensing)
- 第二层：接口选择 (RS422 vs RS485)

### 4. 硬件初始化
- 添加了USART0的GPIO配置 (PA9/PA10)
- 配置了USART0的中断处理
- 设置了正确的波特率 (460800)

### 5. 中断处理
实现了USART0_IRQHandler，与现有的数据处理逻辑兼容。

## 文件修改清单

| 文件 | 修改内容 | 状态 |
|------|----------|------|
| Protocol/config.h | 添加接口选择宏定义 | ✅ 完成 |
| Protocol/UartDefine.h | 添加COMPLEX_0端口定义 | ✅ 完成 |
| bsp/inc/bsp_fmc.h | 添加COMPLEX_0端口定义 | ✅ 完成 |
| Protocol/computerFrameParse.c | 修改UART初始化逻辑 | ✅ 完成 |
| Source/src/main.c | 添加USART0初始化和中断配置 | ✅ 完成 |
| bsp/src/bsp_uart.c | 添加USART0中断处理函数 | ✅ 完成 |
| bsp/inc/bsp_uart.h | 添加函数声明 | ✅ 完成 |

## 接口对比

| 特性 | RS422模式 | RS485模式 |
|------|-----------|-----------|
| 串口 | UART3 | USART0 |
| 引脚 | PA0(TX), PA1(RX) | PA9(TX), PA10(RX) |
| FPGA端口 | COMPLEX_8 | COMPLEX_0 |
| 波特率 | 460800 | 460800 |
| 模式 | UART_RS422 | UART_RS485 |
| 中断处理 | UART3_IRQHandler | USART0_IRQHandler |

## 使用方法

### 切换到RS485接口：
1. 打开 `Protocol/config.h`
2. 设置：`#define OUTPUT_INTERFACE_SELECT OUTPUT_INTERFACE_RS485`
3. 重新编译项目

### 切换回RS422接口：
1. 打开 `Protocol/config.h`
2. 设置：`#define OUTPUT_INTERFACE_SELECT OUTPUT_INTERFACE_RS422`
3. 重新编译项目

## 验证结果

### 代码验证
- ✅ 所有文件修改正确
- ✅ 宏定义逻辑正确
- ✅ 条件编译语法正确
- ✅ 函数声明和定义匹配

### 配置验证
- ✅ 当前配置为RS485模式
- ✅ 使用USART0 (PA9/PA10)
- ✅ 波特率设置为460800
- ✅ 使用UART_RS485模式

## 注意事项

1. **硬件要求**：确保硬件上RS485收发器正确连接到PA9/PA10引脚
2. **FPGA固件**：确保FPGA固件支持COMPLEX_0端口的RS485模式
3. **编译要求**：修改配置后必须重新编译整个项目
4. **测试建议**：使用示波器或逻辑分析仪验证信号输出

## 项目状态
🎉 **项目完成** - RS485接口实现已完成，可以投入使用。

所有代码修改已经过验证，符合用户需求，实现了从RS422到RS485的完整切换功能。
