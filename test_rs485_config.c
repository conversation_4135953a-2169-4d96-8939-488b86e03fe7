/*
 * RS485配置测试文件
 * 用于验证RS422到RS485的配置修改是否正确
 */

#include "config.h"
#include "UartDefine.h"
#include <stdio.h>

// 测试函数：打印当前配置
void test_rs485_config(void)
{
    printf("=== RS485配置测试 ===\n");
    
    // 打印当前选择的接口
    #if OUTPUT_INTERFACE_SELECT == OUTPUT_INTERFACE_RS485
        printf("当前选择: RS485接口\n");
        printf("串口: USART0\n");
        printf("引脚: PA9(TX), PA10(RX)\n");
        printf("波特率: 460800\n");
        printf("TX端口: UART_TXPORT_COMPLEX_0 (%d)\n", UART_TXPORT_COMPLEX_0);
        printf("RX端口: UART_RXPORT_COMPLEX_0 (%d)\n", UART_RXPORT_COMPLEX_0);
        printf("模式: UART_RS485 (%d)\n", UART_RS485);
    #elif OUTPUT_INTERFACE_SELECT == OUTPUT_INTERFACE_RS422
        printf("当前选择: RS422接口\n");
        printf("串口: UART3\n");
        printf("引脚: PA0(TX), PA1(RX)\n");
        printf("波特率: 460800\n");
        printf("TX端口: UART_TXPORT_COMPLEX_8 (%d)\n", UART_TXPORT_COMPLEX_8);
        printf("RX端口: UART_RXPORT_COMPLEX_8 (%d)\n", UART_RXPORT_COMPLEX_8);
        printf("模式: UART_RS422 (%d)\n", UART_RS422);
    #else
        printf("错误: 未知的接口选择\n");
    #endif
    
    // 打印协议配置
    #if configProtocolUse == ProtocolCfg_iPMV
        printf("协议: iPMV (波特率230400)\n");
    #else
        printf("协议: asensing (波特率460800)\n");
    #endif
    
    printf("===================\n");
}

// 模拟初始化函数调用
void simulate_uart_init(void)
{
    printf("\n=== 模拟UART初始化 ===\n");
    
    #if configProtocolUse == ProtocolCfg_iPMV
        #if OUTPUT_INTERFACE_SELECT == OUTPUT_INTERFACE_RS485
            printf("调用: Uart_TxInit(UART_TXPORT_COMPLEX_0, UART_BAUDRATE_230400BPS, UART_PARITY_NONE, UART_STOPBIT_ONE, UART_RS485, UART_ENABLE)\n");
            printf("调用: Uart_RxInit(UART_RXPORT_COMPLEX_0, UART_BAUDRATE_230400BPS, UART_PARITY_NONE, UART_STOPBIT_ONE, UART_RS485, UART_ENABLE)\n");
        #else
            printf("调用: Uart_TxInit(UART_TXPORT_COMPLEX_8, UART_BAUDRATE_230400BPS, UART_PARITY_NONE, UART_STOPBIT_ONE, UART_RS422, UART_ENABLE)\n");
            printf("调用: Uart_RxInit(UART_RXPORT_COMPLEX_8, UART_BAUDRATE_230400BPS, UART_PARITY_NONE, UART_STOPBIT_ONE, UART_RS422, UART_ENABLE)\n");
        #endif
    #else
        #if OUTPUT_INTERFACE_SELECT == OUTPUT_INTERFACE_RS485
            printf("调用: Uart_TxInit(UART_TXPORT_COMPLEX_0, UART_BAUDRATE_460800BPS, UART_PARITY_NONE, UART_STOPBIT_ONE, UART_RS485, UART_ENABLE)\n");
            printf("调用: Uart_RxInit(UART_RXPORT_COMPLEX_0, UART_BAUDRATE_460800BPS, UART_PARITY_NONE, UART_STOPBIT_ONE, UART_RS485, UART_ENABLE)\n");
        #else
            printf("调用: Uart_TxInit(UART_TXPORT_COMPLEX_8, UART_BAUDRATE_460800BPS, UART_PARITY_NONE, UART_STOPBIT_ONE, UART_RS422, UART_ENABLE)\n");
            printf("调用: Uart_RxInit(UART_RXPORT_COMPLEX_8, UART_BAUDRATE_460800BPS, UART_PARITY_NONE, UART_STOPBIT_ONE, UART_RS422, UART_ENABLE)\n");
        #endif
    #endif
    
    printf("====================\n");
}

int main(void)
{
    test_rs485_config();
    simulate_uart_init();
    return 0;
}
